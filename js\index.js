"use strict";
let cartona = "";
for (let i = 0; i < 18; i += 1) {
  if (i % 2 == 0) {
    cartona += `
    <button class="btn">
           <i class="fa-brands fa-instagram display-6 pb-5 text-white"></i>
     </button>
   `;
  } else {
    cartona += `
    <button class="btn">
           <i class="fa-brands fa-facebook display-6 pb-5 text-primary"></i>
     </button>
   `;
  }
}
document.getElementById("cartona").innerHTML = cartona;
function toggle() {
  $("header").hide(1000).show(1000);
}
for (let i = 0; i < 1; i++) {
  toggle();
}
$("button").after(`<button class="btn">
           <i class="fa-brands fa-whatsapp display-6 pb-5 text-danger"></i>
     </button>`);
function up() {
  $(window).scrollTop(0);
}
$("#up").on("click", up());
